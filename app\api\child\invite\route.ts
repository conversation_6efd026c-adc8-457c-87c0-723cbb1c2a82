import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { isDemoMode } from '@/app/config/demoMode';
import { ChildUserService } from '@/lib/services/childUserService';
import { UserService } from '@/lib/services/userService';

// Send invitation to child user
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { childEmail } = await request.json();

    if (!childEmail) {
      return NextResponse.json(
        { error: 'Child email is required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(childEmail)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Get user from database to get the internal user ID
    const dbUser = await UserService.findBySupabaseId(user.id);
    if (!dbUser) {
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    const demoMode = isDemoMode();

    // Send invitation using database service (works for both demo and production)
    const invitation = await ChildUserService.inviteChildUser(
      dbUser.email,
      childEmail,
      dbUser.id
    );

    console.log(`[${demoMode ? 'DEMO' : 'PROD'} MODE] Invitation sent from ${dbUser.email} to ${childEmail}:`, invitation);

    return NextResponse.json({
      success: true,
      invitation,
      message: `Invitation sent to ${childEmail}${demoMode ? ' (Demo Mode)' : ''}`,
      demoMode
    });

  } catch (error) {
    console.error('Error sending invitation:', error);

    // Handle specific error messages from mock API
    if (error instanceof Error) {
      if (error.message.includes('already connected') || error.message.includes('already sent')) {
        return NextResponse.json(
          { error: error.message },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      {
        error: 'Failed to send invitation',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
