import { prisma } from '@/lib/prisma';
import { User, MasterChildRelationship, Invitation, InvitationStatus, UserRole } from '@prisma/client';
import { InvitationService } from './invitationService';

export interface ChildUserInfo {
  id: string;
  email: string;
  name: string;
  masterId: string;
  connectedAt: string;
  status: 'active' | 'pending' | 'inactive';
  zerodhaUserId?: string;
  lastActiveAt?: string;
}

export interface PendingInvitation {
  id: string;
  masterEmail: string;
  childEmail: string;
  masterId: string;
  token: string;
  status: 'pending' | 'accepted' | 'expired' | 'cancelled';
  sentAt: string;
  expiresAt: string;
  acceptedAt?: string;
}

export class ChildUserService {
  // Get child users for a master
  static async getChildUsers(masterId: string): Promise<ChildUserInfo[]> {
    const relationships = await prisma.masterChildRelationship.findMany({
      where: {
        masterId,
        isActive: true,
        deletedAt: null
      },
      include: {
        child: {
          include: {
            zerodhaCredentials: true
          }
        }
      },
      orderBy: {
        connectedAt: 'desc'
      }
    });

    return relationships.map(rel => ({
      id: rel.child.id,
      email: rel.child.email,
      name: rel.child.name || rel.child.email.split('@')[0],
      masterId: rel.masterId,
      connectedAt: rel.connectedAt.toISOString(),
      status: rel.child.isActive ? 'active' : 'inactive',
      zerodhaUserId: rel.child.zerodhaCredentials?.zerodhaUserId || undefined,
      lastActiveAt: rel.child.updatedAt.toISOString()
    }));
  }

  // Send invitation to child user
  static async inviteChildUser(
    masterEmail: string,
    childEmail: string,
    masterId: string
  ): Promise<PendingInvitation> {
    // Check if child is already connected
    const existingChild = await prisma.user.findUnique({
      where: { email: childEmail },
      include: {
        childRelationships: {
          where: {
            masterId,
            isActive: true,
            deletedAt: null
          }
        }
      }
    });

    if (existingChild && existingChild.childRelationships.length > 0) {
      throw new Error('Child user is already connected');
    }

    // Check if invitation already exists
    const existingInvitation = await prisma.invitation.findFirst({
      where: {
        receiverEmail: childEmail,
        senderId: masterId,
        status: InvitationStatus.PENDING,
        expiresAt: {
          gt: new Date()
        }
      }
    });

    if (existingInvitation) {
      throw new Error('Invitation already sent to this email');
    }

    // Create invitation using InvitationService
    const invitation = await InvitationService.createInvitation({
      senderEmail: masterEmail,
      receiverEmail: childEmail,
      senderId: masterId,
      type: 'CHILD_INVITATION',
      expiresInDays: 7
    });

    return {
      id: invitation.id,
      masterEmail,
      childEmail,
      masterId,
      token: invitation.token,
      status: 'pending',
      sentAt: invitation.createdAt.toISOString(),
      expiresAt: invitation.expiresAt.toISOString()
    };
  }

  // Accept invitation and connect child user
  static async acceptInvitation(
    token: string,
    childUserData: {
      email: string;
      name?: string;
      zerodhaUserId?: string;
    }
  ): Promise<ChildUserInfo> {
    // Find and validate invitation
    const invitation = await prisma.invitation.findUnique({
      where: { token },
      include: {
        sender: true
      }
    });

    if (!invitation) {
      throw new Error('Invalid invitation token');
    }

    if (invitation.status !== InvitationStatus.PENDING) {
      throw new Error('Invitation has already been processed');
    }

    if (invitation.expiresAt < new Date()) {
      throw new Error('Invitation has expired');
    }

    // Create or get child user
    let childUser = await prisma.user.findUnique({
      where: { email: childUserData.email }
    });

    if (!childUser) {
      childUser = await prisma.user.create({
        data: {
          email: childUserData.email,
          name: childUserData.name,
          role: UserRole.CHILD,
          isDemo: invitation.sender.isDemo // Inherit demo status from master
        }
      });
    }

    // Create master-child relationship
    const relationship = await prisma.masterChildRelationship.create({
      data: {
        masterId: invitation.senderId,
        childId: childUser.id,
        isActive: true
      }
    });

    // Update invitation status
    await prisma.invitation.update({
      where: { id: invitation.id },
      data: {
        status: InvitationStatus.ACCEPTED,
        acceptedAt: new Date()
      }
    });

    // Create Zerodha credentials if provided
    if (childUserData.zerodhaUserId) {
      await prisma.zerodhaCredentials.create({
        data: {
          userId: childUser.id,
          zerodhaUserId: childUserData.zerodhaUserId,
          isConnected: false // Child will connect separately
        }
      });
    }

    return {
      id: childUser.id,
      email: childUser.email,
      name: childUser.name || childUser.email.split('@')[0],
      masterId: invitation.senderId,
      connectedAt: relationship.connectedAt.toISOString(),
      status: 'active',
      zerodhaUserId: childUserData.zerodhaUserId
    };
  }

  // Remove child user connection
  static async removeChildUser(childId: string, masterId: string): Promise<boolean> {
    try {
      const result = await prisma.masterChildRelationship.updateMany({
        where: {
          childId,
          masterId,
          isActive: true
        },
        data: {
          isActive: false,
          deletedAt: new Date()
        }
      });

      return result.count > 0;
    } catch (error) {
      console.error('Error removing child user:', error);
      return false;
    }
  }

  // Get pending invitations for a master
  static async getPendingInvitations(masterId: string): Promise<PendingInvitation[]> {
    const invitations = await prisma.invitation.findMany({
      where: {
        senderId: masterId,
        status: InvitationStatus.PENDING,
        expiresAt: {
          gt: new Date()
        }
      },
      include: {
        sender: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return invitations.map(inv => ({
      id: inv.id,
      masterEmail: inv.sender.email,
      childEmail: inv.receiverEmail,
      masterId: inv.senderId,
      token: inv.token,
      status: 'pending',
      sentAt: inv.createdAt.toISOString(),
      expiresAt: inv.expiresAt.toISOString()
    }));
  }

  // Cancel invitation
  static async cancelInvitation(invitationId: string, masterId: string): Promise<boolean> {
    try {
      const result = await prisma.invitation.updateMany({
        where: {
          id: invitationId,
          senderId: masterId,
          status: InvitationStatus.PENDING
        },
        data: {
          status: InvitationStatus.CANCELLED
        }
      });

      return result.count > 0;
    } catch (error) {
      console.error('Error cancelling invitation:', error);
      return false;
    }
  }

  // Get child user count for a master
  static async getChildUserCount(masterId: string): Promise<number> {
    return prisma.masterChildRelationship.count({
      where: {
        masterId,
        isActive: true,
        deletedAt: null
      }
    });
  }

  // Check if user is a child of a specific master
  static async isChildOfMaster(childId: string, masterId: string): Promise<boolean> {
    const relationship = await prisma.masterChildRelationship.findFirst({
      where: {
        childId,
        masterId,
        isActive: true,
        deletedAt: null
      }
    });

    return relationship !== null;
  }

  // Get master for a child user
  static async getMasterForChild(childId: string): Promise<User | null> {
    const relationship = await prisma.masterChildRelationship.findFirst({
      where: {
        childId,
        isActive: true,
        deletedAt: null
      },
      include: {
        master: true
      }
    });

    return relationship?.master || null;
  }
}
